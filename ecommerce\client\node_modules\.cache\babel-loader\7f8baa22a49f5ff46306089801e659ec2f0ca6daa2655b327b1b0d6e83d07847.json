{"ast": null, "code": "var _jsxFileName = \"F:\\\\DJANGO\\\\ecommerce\\\\client\\\\src\\\\components\\\\Navigation.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { isAuthenticated, logout } from \"../api/auth\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const [authenticated, setAuthenticated] = useState(false);\n  const [cartCount, setCartCount] = useState(0);\n  const navigate = useNavigate();\n  useEffect(() => {\n    setAuthenticated(isAuthenticated());\n    updateCartCount();\n\n    // Listen for storage changes to update cart count\n    const handleStorageChange = () => {\n      updateCartCount();\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, []);\n  const updateCartCount = () => {\n    const cart = JSON.parse(localStorage.getItem(\"cart\")) || [];\n    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);\n    setCartCount(totalItems);\n  };\n  const handleLogout = () => {\n    logout();\n    setAuthenticated(false);\n    navigate(\"/\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    style: {\n      backgroundColor: \"#343a40\",\n      padding: \"1rem 2rem\",\n      marginBottom: \"20px\"\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        maxWidth: \"1200px\",\n        margin: \"0 auto\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        style: {\n          color: \"white\",\n          textDecoration: \"none\",\n          fontSize: \"1.5rem\",\n          fontWeight: \"bold\"\n        },\n        children: \"E-Commerce Store\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          style: {\n            color: \"white\",\n            textDecoration: \"none\",\n            padding: \"8px 16px\",\n            borderRadius: \"4px\",\n            transition: \"background-color 0.3s\"\n          },\n          onMouseOver: e => e.target.style.backgroundColor = \"#495057\",\n          onMouseOut: e => e.target.style.backgroundColor = \"transparent\",\n          children: \"Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/cart\",\n          style: {\n            color: \"white\",\n            textDecoration: \"none\",\n            padding: \"8px 16px\",\n            borderRadius: \"4px\",\n            position: \"relative\",\n            transition: \"background-color 0.3s\"\n          },\n          onMouseOver: e => e.target.style.backgroundColor = \"#495057\",\n          onMouseOut: e => e.target.style.backgroundColor = \"transparent\",\n          children: [\"Cart\", cartCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: \"absolute\",\n              top: \"-5px\",\n              right: \"5px\",\n              backgroundColor: \"#dc3545\",\n              color: \"white\",\n              borderRadius: \"50%\",\n              width: \"20px\",\n              height: \"20px\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              fontSize: \"12px\",\n              fontWeight: \"bold\"\n            },\n            children: cartCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), authenticated ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          style: {\n            backgroundColor: \"#dc3545\",\n            color: \"white\",\n            border: \"none\",\n            padding: \"8px 16px\",\n            borderRadius: \"4px\",\n            cursor: \"pointer\",\n            fontSize: \"14px\"\n          },\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            gap: \"10px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            style: {\n              color: \"white\",\n              textDecoration: \"none\",\n              padding: \"8px 16px\",\n              border: \"1px solid white\",\n              borderRadius: \"4px\",\n              transition: \"all 0.3s\"\n            },\n            onMouseOver: e => {\n              e.target.style.backgroundColor = \"white\";\n              e.target.style.color = \"#343a40\";\n            },\n            onMouseOut: e => {\n              e.target.style.backgroundColor = \"transparent\";\n              e.target.style.color = \"white\";\n            },\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            style: {\n              backgroundColor: \"#007bff\",\n              color: \"white\",\n              textDecoration: \"none\",\n              padding: \"8px 16px\",\n              borderRadius: \"4px\",\n              transition: \"background-color 0.3s\"\n            },\n            onMouseOver: e => e.target.style.backgroundColor = \"#0056b3\",\n            onMouseOut: e => e.target.style.backgroundColor = \"#007bff\",\n            children: \"Register\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(Navigation, \"5t4WNJ6ffMtOQRLd/R1OZCFgnkk=\", false, function () {\n  return [useNavigate];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "isAuthenticated", "logout", "jsxDEV", "_jsxDEV", "Navigation", "_s", "authenticated", "setAuthenticated", "cartCount", "setCartCount", "navigate", "updateCartCount", "handleStorageChange", "window", "addEventListener", "removeEventListener", "cart", "JSON", "parse", "localStorage", "getItem", "totalItems", "reduce", "sum", "item", "quantity", "handleLogout", "style", "backgroundColor", "padding", "marginBottom", "children", "display", "justifyContent", "alignItems", "max<PERSON><PERSON><PERSON>", "margin", "to", "color", "textDecoration", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "borderRadius", "transition", "onMouseOver", "e", "target", "onMouseOut", "position", "top", "right", "width", "height", "onClick", "border", "cursor", "_c", "$RefreshReg$"], "sources": ["F:/DJANGO/ecommerce/client/src/components/Navigation.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { isAuthenticated, logout } from \"../api/auth\";\n\nconst Navigation = () => {\n  const [authenticated, setAuthenticated] = useState(false);\n  const [cartCount, setCartCount] = useState(0);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    setAuthenticated(isAuthenticated());\n    updateCartCount();\n    \n    // Listen for storage changes to update cart count\n    const handleStorageChange = () => {\n      updateCartCount();\n    };\n    \n    window.addEventListener('storage', handleStorageChange);\n    \n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, []);\n\n  const updateCartCount = () => {\n    const cart = JSON.parse(localStorage.getItem(\"cart\")) || [];\n    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);\n    setCartCount(totalItems);\n  };\n\n  const handleLogout = () => {\n    logout();\n    setAuthenticated(false);\n    navigate(\"/\");\n  };\n\n  return (\n    <nav style={{\n      backgroundColor: \"#343a40\",\n      padding: \"1rem 2rem\",\n      marginBottom: \"20px\"\n    }}>\n      <div style={{\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        maxWidth: \"1200px\",\n        margin: \"0 auto\"\n      }}>\n        <Link \n          to=\"/\" \n          style={{\n            color: \"white\",\n            textDecoration: \"none\",\n            fontSize: \"1.5rem\",\n            fontWeight: \"bold\"\n          }}\n        >\n          E-Commerce Store\n        </Link>\n        \n        <div style={{ display: \"flex\", alignItems: \"center\", gap: \"20px\" }}>\n          <Link \n            to=\"/\" \n            style={{\n              color: \"white\",\n              textDecoration: \"none\",\n              padding: \"8px 16px\",\n              borderRadius: \"4px\",\n              transition: \"background-color 0.3s\"\n            }}\n            onMouseOver={(e) => e.target.style.backgroundColor = \"#495057\"}\n            onMouseOut={(e) => e.target.style.backgroundColor = \"transparent\"}\n          >\n            Products\n          </Link>\n          \n          <Link \n            to=\"/cart\" \n            style={{\n              color: \"white\",\n              textDecoration: \"none\",\n              padding: \"8px 16px\",\n              borderRadius: \"4px\",\n              position: \"relative\",\n              transition: \"background-color 0.3s\"\n            }}\n            onMouseOver={(e) => e.target.style.backgroundColor = \"#495057\"}\n            onMouseOut={(e) => e.target.style.backgroundColor = \"transparent\"}\n          >\n            Cart\n            {cartCount > 0 && (\n              <span style={{\n                position: \"absolute\",\n                top: \"-5px\",\n                right: \"5px\",\n                backgroundColor: \"#dc3545\",\n                color: \"white\",\n                borderRadius: \"50%\",\n                width: \"20px\",\n                height: \"20px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                fontSize: \"12px\",\n                fontWeight: \"bold\"\n              }}>\n                {cartCount}\n              </span>\n            )}\n          </Link>\n          \n          {authenticated ? (\n            <button\n              onClick={handleLogout}\n              style={{\n                backgroundColor: \"#dc3545\",\n                color: \"white\",\n                border: \"none\",\n                padding: \"8px 16px\",\n                borderRadius: \"4px\",\n                cursor: \"pointer\",\n                fontSize: \"14px\"\n              }}\n            >\n              Logout\n            </button>\n          ) : (\n            <div style={{ display: \"flex\", gap: \"10px\" }}>\n              <Link \n                to=\"/login\" \n                style={{\n                  color: \"white\",\n                  textDecoration: \"none\",\n                  padding: \"8px 16px\",\n                  border: \"1px solid white\",\n                  borderRadius: \"4px\",\n                  transition: \"all 0.3s\"\n                }}\n                onMouseOver={(e) => {\n                  e.target.style.backgroundColor = \"white\";\n                  e.target.style.color = \"#343a40\";\n                }}\n                onMouseOut={(e) => {\n                  e.target.style.backgroundColor = \"transparent\";\n                  e.target.style.color = \"white\";\n                }}\n              >\n                Login\n              </Link>\n              \n              <Link \n                to=\"/register\" \n                style={{\n                  backgroundColor: \"#007bff\",\n                  color: \"white\",\n                  textDecoration: \"none\",\n                  padding: \"8px 16px\",\n                  borderRadius: \"4px\",\n                  transition: \"background-color 0.3s\"\n                }}\n                onMouseOver={(e) => e.target.style.backgroundColor = \"#0056b3\"}\n                onMouseOut={(e) => e.target.style.backgroundColor = \"#007bff\"}\n              >\n                Register\n              </Link>\n            </div>\n          )}\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,eAAe,EAAEC,MAAM,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAMc,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACdU,gBAAgB,CAACP,eAAe,CAAC,CAAC,CAAC;IACnCW,eAAe,CAAC,CAAC;;IAEjB;IACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;MAChCD,eAAe,CAAC,CAAC;IACnB,CAAC;IAEDE,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEF,mBAAmB,CAAC;IAEvD,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEH,mBAAmB,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMK,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;IAC3D,MAAMC,UAAU,GAAGL,IAAI,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC;IACrEhB,YAAY,CAACY,UAAU,CAAC;EAC1B,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzBzB,MAAM,CAAC,CAAC;IACRM,gBAAgB,CAAC,KAAK,CAAC;IACvBG,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEP,OAAA;IAAKwB,KAAK,EAAE;MACVC,eAAe,EAAE,SAAS;MAC1BC,OAAO,EAAE,WAAW;MACpBC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,eACA5B,OAAA;MAAKwB,KAAK,EAAE;QACVK,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,gBACA5B,OAAA,CAACL,IAAI;QACHuC,EAAE,EAAC,GAAG;QACNV,KAAK,EAAE;UACLW,KAAK,EAAE,OAAO;UACdC,cAAc,EAAE,MAAM;UACtBC,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,EACH;MAED;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEP1C,OAAA;QAAKwB,KAAK,EAAE;UAAEK,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEY,GAAG,EAAE;QAAO,CAAE;QAAAf,QAAA,gBACjE5B,OAAA,CAACL,IAAI;UACHuC,EAAE,EAAC,GAAG;UACNV,KAAK,EAAE;YACLW,KAAK,EAAE,OAAO;YACdC,cAAc,EAAE,MAAM;YACtBV,OAAO,EAAE,UAAU;YACnBkB,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE;UACd,CAAE;UACFC,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACxB,KAAK,CAACC,eAAe,GAAG,SAAU;UAC/DwB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACxB,KAAK,CAACC,eAAe,GAAG,aAAc;UAAAG,QAAA,EACnE;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEP1C,OAAA,CAACL,IAAI;UACHuC,EAAE,EAAC,OAAO;UACVV,KAAK,EAAE;YACLW,KAAK,EAAE,OAAO;YACdC,cAAc,EAAE,MAAM;YACtBV,OAAO,EAAE,UAAU;YACnBkB,YAAY,EAAE,KAAK;YACnBM,QAAQ,EAAE,UAAU;YACpBL,UAAU,EAAE;UACd,CAAE;UACFC,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACxB,KAAK,CAACC,eAAe,GAAG,SAAU;UAC/DwB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACxB,KAAK,CAACC,eAAe,GAAG,aAAc;UAAAG,QAAA,GACnE,MAEC,EAACvB,SAAS,GAAG,CAAC,iBACZL,OAAA;YAAMwB,KAAK,EAAE;cACX0B,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,MAAM;cACXC,KAAK,EAAE,KAAK;cACZ3B,eAAe,EAAE,SAAS;cAC1BU,KAAK,EAAE,OAAO;cACdS,YAAY,EAAE,KAAK;cACnBS,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdzB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,QAAQ;cACxBO,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAV,QAAA,EACCvB;UAAS;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EAENvC,aAAa,gBACZH,OAAA;UACEuD,OAAO,EAAEhC,YAAa;UACtBC,KAAK,EAAE;YACLC,eAAe,EAAE,SAAS;YAC1BU,KAAK,EAAE,OAAO;YACdqB,MAAM,EAAE,MAAM;YACd9B,OAAO,EAAE,UAAU;YACnBkB,YAAY,EAAE,KAAK;YACnBa,MAAM,EAAE,SAAS;YACjBpB,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,EACH;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAET1C,OAAA;UAAKwB,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEc,GAAG,EAAE;UAAO,CAAE;UAAAf,QAAA,gBAC3C5B,OAAA,CAACL,IAAI;YACHuC,EAAE,EAAC,QAAQ;YACXV,KAAK,EAAE;cACLW,KAAK,EAAE,OAAO;cACdC,cAAc,EAAE,MAAM;cACtBV,OAAO,EAAE,UAAU;cACnB8B,MAAM,EAAE,iBAAiB;cACzBZ,YAAY,EAAE,KAAK;cACnBC,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGC,CAAC,IAAK;cAClBA,CAAC,CAACC,MAAM,CAACxB,KAAK,CAACC,eAAe,GAAG,OAAO;cACxCsB,CAAC,CAACC,MAAM,CAACxB,KAAK,CAACW,KAAK,GAAG,SAAS;YAClC,CAAE;YACFc,UAAU,EAAGF,CAAC,IAAK;cACjBA,CAAC,CAACC,MAAM,CAACxB,KAAK,CAACC,eAAe,GAAG,aAAa;cAC9CsB,CAAC,CAACC,MAAM,CAACxB,KAAK,CAACW,KAAK,GAAG,OAAO;YAChC,CAAE;YAAAP,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEP1C,OAAA,CAACL,IAAI;YACHuC,EAAE,EAAC,WAAW;YACdV,KAAK,EAAE;cACLC,eAAe,EAAE,SAAS;cAC1BU,KAAK,EAAE,OAAO;cACdC,cAAc,EAAE,MAAM;cACtBV,OAAO,EAAE,UAAU;cACnBkB,YAAY,EAAE,KAAK;cACnBC,UAAU,EAAE;YACd,CAAE;YACFC,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACxB,KAAK,CAACC,eAAe,GAAG,SAAU;YAC/DwB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACxB,KAAK,CAACC,eAAe,GAAG,SAAU;YAAAG,QAAA,EAC/D;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CAzKID,UAAU;EAAA,QAGGL,WAAW;AAAA;AAAA8D,EAAA,GAHxBzD,UAAU;AA2KhB,eAAeA,UAAU;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}