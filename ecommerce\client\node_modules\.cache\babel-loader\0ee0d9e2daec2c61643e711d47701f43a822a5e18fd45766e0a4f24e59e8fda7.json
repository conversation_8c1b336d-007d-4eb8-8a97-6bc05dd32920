{"ast": null, "code": "import axios from \"axios\";\nconst BASE_URL = \"http://localhost:8000/api/\";\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Fetch all products\nexport const fetchProducts = async () => {\n  try {\n    const response = await api.get('products/');\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Fetch single product by ID\nexport const fetchProduct = async id => {\n  try {\n    const response = await api.get(`products/${id}/`);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Create new product (admin only)\nexport const createProduct = async productData => {\n  try {\n    const response = await api.post('products/', productData);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Update product (admin only)\nexport const updateProduct = async (id, productData) => {\n  try {\n    const response = await api.put(`products/${id}/`, productData);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Delete product (admin only)\nexport const deleteProduct = async id => {\n  try {\n    const response = await api.delete(`products/${id}/`);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "BASE_URL", "api", "create", "baseURL", "headers", "fetchProducts", "response", "get", "error", "fetchProduct", "id", "createProduct", "productData", "post", "updateProduct", "put", "deleteProduct", "delete"], "sources": ["F:/DJANGO/ecommerce/client/src/api/products.js"], "sourcesContent": ["import axios from \"axios\";\n\nconst BASE_URL = \"http://localhost:8000/api/\";\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Fetch all products\nexport const fetchProducts = async () => {\n  try {\n    const response = await api.get('products/');\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Fetch single product by ID\nexport const fetchProduct = async (id) => {\n  try {\n    const response = await api.get(`products/${id}/`);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Create new product (admin only)\nexport const createProduct = async (productData) => {\n  try {\n    const response = await api.post('products/', productData);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Update product (admin only)\nexport const updateProduct = async (id, productData) => {\n  try {\n    const response = await api.put(`products/${id}/`, productData);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Delete product (admin only)\nexport const deleteProduct = async (id) => {\n  try {\n    const response = await api.delete(`products/${id}/`);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,QAAQ,GAAG,4BAA4B;;AAE7C;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,QAAQ;EACjBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,GAAG,CAAC,WAAW,CAAC;IAC3C,OAAOD,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG,MAAOC,EAAE,IAAK;EACxC,IAAI;IACF,MAAMJ,QAAQ,GAAG,MAAML,GAAG,CAACM,GAAG,CAAC,YAAYG,EAAE,GAAG,CAAC;IACjD,OAAOJ,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,aAAa,GAAG,MAAOC,WAAW,IAAK;EAClD,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAML,GAAG,CAACY,IAAI,CAAC,WAAW,EAAED,WAAW,CAAC;IACzD,OAAON,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,aAAa,GAAG,MAAAA,CAAOJ,EAAE,EAAEE,WAAW,KAAK;EACtD,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAML,GAAG,CAACc,GAAG,CAAC,YAAYL,EAAE,GAAG,EAAEE,WAAW,CAAC;IAC9D,OAAON,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMQ,aAAa,GAAG,MAAON,EAAE,IAAK;EACzC,IAAI;IACF,MAAMJ,QAAQ,GAAG,MAAML,GAAG,CAACgB,MAAM,CAAC,YAAYP,EAAE,GAAG,CAAC;IACpD,OAAOJ,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;AAED,eAAeP,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}