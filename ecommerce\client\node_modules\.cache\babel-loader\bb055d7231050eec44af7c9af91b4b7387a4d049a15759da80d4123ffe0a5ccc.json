{"ast": null, "code": "var _jsxFileName = \"F:\\\\DJANGO\\\\ecommerce\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { useEffect } from \"react\";\nimport { BrowserRouter as Router, Route, Routes } from \"react-router-dom\";\nimport { initializeAuth } from \"./api/auth\";\nimport Navigation from \"./components/Navigation\";\nimport LoginPage from \"./pages/LoginPage\";\nimport RegisterPage from \"./pages/RegisterPage\";\nimport ProductsPage from \"./pages/ProductsPage\";\nimport CartPage from \"./pages/CartPage\";\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  useEffect(() => {\n    // Initialize authentication on app start\n    initializeAuth();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(ProductsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cart\",\n            element: /*#__PURE__*/_jsxDEV(CartPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "initializeAuth", "Navigation", "LoginPage", "RegisterPage", "ProductsPage", "CartPage", "jsxDEV", "_jsxDEV", "App", "_s", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["F:/DJANGO/ecommerce/client/src/App.js"], "sourcesContent": ["import { useEffect } from \"react\";\nimport { BrowserRouter as Router, Route, Routes } from \"react-router-dom\";\nimport { initializeAuth } from \"./api/auth\";\nimport Navigation from \"./components/Navigation\";\nimport LoginPage from \"./pages/LoginPage\";\nimport RegisterPage from \"./pages/RegisterPage\";\nimport ProductsPage from \"./pages/ProductsPage\";\nimport CartPage from \"./pages/CartPage\";\nimport './App.css';\n\nfunction App() {\n  useEffect(() => {\n    // Initialize authentication on app start\n    initializeAuth();\n  }, []);\n\n  return (\n    <Router>\n      <div className=\"App\">\n        <Navigation />\n        <main>\n          <Routes>\n            <Route path=\"/\" element={<ProductsPage />} />\n            <Route path=\"/login\" element={<LoginPage />} />\n            <Route path=\"/register\" element={<RegisterPage />} />\n            <Route path=\"/cart\" element={<CartPage />} />\n          </Routes>\n        </main>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AACzE,SAASC,cAAc,QAAQ,YAAY;AAC3C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACbd,SAAS,CAAC,MAAM;IACd;IACAK,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEO,OAAA,CAACV,MAAM;IAAAa,QAAA,eACLH,OAAA;MAAKI,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBH,OAAA,CAACN,UAAU;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACdR,OAAA;QAAAG,QAAA,eACEH,OAAA,CAACR,MAAM;UAAAW,QAAA,gBACLH,OAAA,CAACT,KAAK;YAACkB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEV,OAAA,CAACH,YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CR,OAAA,CAACT,KAAK;YAACkB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEV,OAAA,CAACL,SAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CR,OAAA,CAACT,KAAK;YAACkB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEV,OAAA,CAACJ,YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDR,OAAA,CAACT,KAAK;YAACkB,IAAI,EAAC,OAAO;YAACC,OAAO,eAAEV,OAAA,CAACF,QAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACN,EAAA,CArBQD,GAAG;AAAAU,EAAA,GAAHV,GAAG;AAuBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}