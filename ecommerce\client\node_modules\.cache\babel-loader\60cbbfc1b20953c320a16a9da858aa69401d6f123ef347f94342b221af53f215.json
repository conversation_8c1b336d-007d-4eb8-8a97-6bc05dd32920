{"ast": null, "code": "var _jsxFileName = \"F:\\\\DJANGO\\\\ecommerce\\\\client\\\\src\\\\pages\\\\RegisterPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { register } from \"../api/auth\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RegisterPage = () => {\n  _s();\n  const [form, setForm] = useState({\n    username: \"\",\n    email: \"\",\n    password: \"\",\n    password2: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    // Basic validation\n    if (form.password !== form.password2) {\n      setError(\"Passwords do not match\");\n      setLoading(false);\n      return;\n    }\n    try {\n      await register({\n        username: form.username,\n        email: form.email,\n        password: form.password\n      });\n      alert(\"Registration successful! Please login.\");\n      navigate(\"/login\");\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      console.error(\"Registration error:\", error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Registration failed. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: \"400px\",\n      margin: \"50px auto\",\n      padding: \"20px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Register\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: \"red\",\n        marginBottom: \"15px\",\n        padding: \"10px\",\n        border: \"1px solid red\",\n        borderRadius: \"4px\"\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"15px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"username\",\n          placeholder: \"Username\",\n          value: form.username,\n          onChange: handleChange,\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"10px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"15px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          name: \"email\",\n          placeholder: \"Email\",\n          value: form.email,\n          onChange: handleChange,\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"10px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"15px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          name: \"password\",\n          placeholder: \"Password\",\n          value: form.password,\n          onChange: handleChange,\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"10px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"15px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          name: \"password2\",\n          placeholder: \"Confirm Password\",\n          value: form.password2,\n          onChange: handleChange,\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"10px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        style: {\n          width: \"100%\",\n          padding: \"12px\",\n          backgroundColor: loading ? \"#ccc\" : \"#28a745\",\n          color: \"white\",\n          border: \"none\",\n          borderRadius: \"4px\",\n          fontSize: \"16px\",\n          cursor: loading ? \"not-allowed\" : \"pointer\"\n        },\n        children: loading ? \"Registering...\" : \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        textAlign: \"center\",\n        marginTop: \"20px\"\n      },\n      children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/login\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 34\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(RegisterPage, \"j8DaGM8lsGeOpeklYusQllKwnS8=\", false, function () {\n  return [useNavigate];\n});\n_c = RegisterPage;\nexport default RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "register", "jsxDEV", "_jsxDEV", "RegisterPage", "_s", "form", "setForm", "username", "email", "password", "password2", "loading", "setLoading", "error", "setError", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "alert", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "console", "response", "data", "detail", "message", "style", "max<PERSON><PERSON><PERSON>", "margin", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "marginBottom", "border", "borderRadius", "onSubmit", "type", "placeholder", "onChange", "required", "width", "fontSize", "disabled", "backgroundColor", "cursor", "textAlign", "marginTop", "href", "_c", "$RefreshReg$"], "sources": ["F:/DJANGO/ecommerce/client/src/pages/RegisterPage.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { register } from \"../api/auth\";\n\nconst RegisterPage = () => {\n  const [form, setForm] = useState({\n    username: \"\",\n    email: \"\",\n    password: \"\",\n    password2: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    // Basic validation\n    if (form.password !== form.password2) {\n      setError(\"Passwords do not match\");\n      setLoading(false);\n      return;\n    }\n\n    try {\n      await register({\n        username: form.username,\n        email: form.email,\n        password: form.password\n      });\n      \n      alert(\"Registration successful! Please login.\");\n      navigate(\"/login\");\n      \n    } catch (error) {\n      console.error(\"Registration error:\", error);\n      setError(\n        error.response?.data?.detail || \n        error.response?.data?.message || \n        \"Registration failed. Please try again.\"\n      );\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div style={{ maxWidth: \"400px\", margin: \"50px auto\", padding: \"20px\" }}>\n      <h2>Register</h2>\n      \n      {error && (\n        <div style={{ \n          color: \"red\", \n          marginBottom: \"15px\", \n          padding: \"10px\", \n          border: \"1px solid red\", \n          borderRadius: \"4px\" \n        }}>\n          {error}\n        </div>\n      )}\n      \n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: \"15px\" }}>\n          <input\n            type=\"text\"\n            name=\"username\"\n            placeholder=\"Username\"\n            value={form.username}\n            onChange={handleChange}\n            required\n            style={{\n              width: \"100%\",\n              padding: \"10px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n        \n        <div style={{ marginBottom: \"15px\" }}>\n          <input\n            type=\"email\"\n            name=\"email\"\n            placeholder=\"Email\"\n            value={form.email}\n            onChange={handleChange}\n            required\n            style={{\n              width: \"100%\",\n              padding: \"10px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n        \n        <div style={{ marginBottom: \"15px\" }}>\n          <input\n            type=\"password\"\n            name=\"password\"\n            placeholder=\"Password\"\n            value={form.password}\n            onChange={handleChange}\n            required\n            style={{\n              width: \"100%\",\n              padding: \"10px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n        \n        <div style={{ marginBottom: \"15px\" }}>\n          <input\n            type=\"password\"\n            name=\"password2\"\n            placeholder=\"Confirm Password\"\n            value={form.password2}\n            onChange={handleChange}\n            required\n            style={{\n              width: \"100%\",\n              padding: \"10px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n        \n        <button \n          type=\"submit\" \n          disabled={loading}\n          style={{\n            width: \"100%\",\n            padding: \"12px\",\n            backgroundColor: loading ? \"#ccc\" : \"#28a745\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\",\n            cursor: loading ? \"not-allowed\" : \"pointer\"\n          }}\n        >\n          {loading ? \"Registering...\" : \"Register\"}\n        </button>\n      </form>\n      \n      <p style={{ textAlign: \"center\", marginTop: \"20px\" }}>\n        Already have an account? <a href=\"/login\">Login</a>\n      </p>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGR,QAAQ,CAAC;IAC/BS,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAMiB,YAAY,GAAIC,CAAC,IAAK;IAC1BX,OAAO,CAAC;MACN,GAAGD,IAAI;MACP,CAACY,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBV,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAIT,IAAI,CAACI,QAAQ,KAAKJ,IAAI,CAACK,SAAS,EAAE;MACpCI,QAAQ,CAAC,wBAAwB,CAAC;MAClCF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF,MAAMZ,QAAQ,CAAC;QACbO,QAAQ,EAAEF,IAAI,CAACE,QAAQ;QACvBC,KAAK,EAAEH,IAAI,CAACG,KAAK;QACjBC,QAAQ,EAAEJ,IAAI,CAACI;MACjB,CAAC,CAAC;MAEFc,KAAK,CAAC,wCAAwC,CAAC;MAC/CR,QAAQ,CAAC,QAAQ,CAAC;IAEpB,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdC,OAAO,CAACf,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CC,QAAQ,CACN,EAAAU,eAAA,GAAAX,KAAK,CAACgB,QAAQ,cAAAL,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBM,IAAI,cAAAL,oBAAA,uBAApBA,oBAAA,CAAsBM,MAAM,OAAAL,gBAAA,GAC5Bb,KAAK,CAACgB,QAAQ,cAAAH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBI,IAAI,cAAAH,qBAAA,uBAApBA,qBAAA,CAAsBK,OAAO,KAC7B,wCACF,CAAC;IACH,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAK+B,KAAK,EAAE;MAAEC,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACtEnC,OAAA;MAAAmC,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEhB5B,KAAK,iBACJX,OAAA;MAAK+B,KAAK,EAAE;QACVS,KAAK,EAAE,KAAK;QACZC,YAAY,EAAE,MAAM;QACpBP,OAAO,EAAE,MAAM;QACfQ,MAAM,EAAE,eAAe;QACvBC,YAAY,EAAE;MAChB,CAAE;MAAAR,QAAA,EACCxB;IAAK;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDvC,OAAA;MAAM4C,QAAQ,EAAEzB,YAAa;MAAAgB,QAAA,gBAC3BnC,OAAA;QAAK+B,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAN,QAAA,eACnCnC,OAAA;UACE6C,IAAI,EAAC,MAAM;UACX5B,IAAI,EAAC,UAAU;UACf6B,WAAW,EAAC,UAAU;UACtB5B,KAAK,EAAEf,IAAI,CAACE,QAAS;UACrB0C,QAAQ,EAAEjC,YAAa;UACvBkC,QAAQ;UACRjB,KAAK,EAAE;YACLkB,KAAK,EAAE,MAAM;YACbf,OAAO,EAAE,MAAM;YACfQ,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE;UACZ;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENvC,OAAA;QAAK+B,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAN,QAAA,eACnCnC,OAAA;UACE6C,IAAI,EAAC,OAAO;UACZ5B,IAAI,EAAC,OAAO;UACZ6B,WAAW,EAAC,OAAO;UACnB5B,KAAK,EAAEf,IAAI,CAACG,KAAM;UAClByC,QAAQ,EAAEjC,YAAa;UACvBkC,QAAQ;UACRjB,KAAK,EAAE;YACLkB,KAAK,EAAE,MAAM;YACbf,OAAO,EAAE,MAAM;YACfQ,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE;UACZ;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENvC,OAAA;QAAK+B,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAN,QAAA,eACnCnC,OAAA;UACE6C,IAAI,EAAC,UAAU;UACf5B,IAAI,EAAC,UAAU;UACf6B,WAAW,EAAC,UAAU;UACtB5B,KAAK,EAAEf,IAAI,CAACI,QAAS;UACrBwC,QAAQ,EAAEjC,YAAa;UACvBkC,QAAQ;UACRjB,KAAK,EAAE;YACLkB,KAAK,EAAE,MAAM;YACbf,OAAO,EAAE,MAAM;YACfQ,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE;UACZ;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENvC,OAAA;QAAK+B,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAN,QAAA,eACnCnC,OAAA;UACE6C,IAAI,EAAC,UAAU;UACf5B,IAAI,EAAC,WAAW;UAChB6B,WAAW,EAAC,kBAAkB;UAC9B5B,KAAK,EAAEf,IAAI,CAACK,SAAU;UACtBuC,QAAQ,EAAEjC,YAAa;UACvBkC,QAAQ;UACRjB,KAAK,EAAE;YACLkB,KAAK,EAAE,MAAM;YACbf,OAAO,EAAE,MAAM;YACfQ,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE;UACZ;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENvC,OAAA;QACE6C,IAAI,EAAC,QAAQ;QACbM,QAAQ,EAAE1C,OAAQ;QAClBsB,KAAK,EAAE;UACLkB,KAAK,EAAE,MAAM;UACbf,OAAO,EAAE,MAAM;UACfkB,eAAe,EAAE3C,OAAO,GAAG,MAAM,GAAG,SAAS;UAC7C+B,KAAK,EAAE,OAAO;UACdE,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBO,QAAQ,EAAE,MAAM;UAChBG,MAAM,EAAE5C,OAAO,GAAG,aAAa,GAAG;QACpC,CAAE;QAAA0B,QAAA,EAED1B,OAAO,GAAG,gBAAgB,GAAG;MAAU;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEPvC,OAAA;MAAG+B,KAAK,EAAE;QAAEuB,SAAS,EAAE,QAAQ;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAApB,QAAA,GAAC,2BAC3B,eAAAnC,OAAA;QAAGwD,IAAI,EAAC,QAAQ;QAAArB,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACrC,EAAA,CApKID,YAAY;EAAA,QASCJ,WAAW;AAAA;AAAA4D,EAAA,GATxBxD,YAAY;AAsKlB,eAAeA,YAAY;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}