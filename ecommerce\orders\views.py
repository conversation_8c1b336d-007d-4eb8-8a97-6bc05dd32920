from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from django.db import transaction
from .models import CartItem, Order
from .serializers import CartItemSerializer, OrderSerializer

class CartViewSet(viewsets.ModelViewSet):
    serializer_class = CartItemSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return CartItem.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class OrderViewSet(viewsets.ModelViewSet):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Order.objects.filter(user=self.request.user)

    def create(self, request, *args, **kwargs):
        cart_items = CartItem.objects.filter(user=request.user)

        if not cart_items.exists():
            return Response(
                {"error": "Cart is empty"},
                status=status.HTTP_400_BAD_REQUEST
            )

        with transaction.atomic():
            total = sum(item.product.price * item.quantity for item in cart_items)
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            order = serializer.save(user=request.user, total_price=total)

            # Add cart items to the order
            order.items.set(cart_items)

            # Clear the cart by creating new cart items for the order
            # and then deleting the original cart items
            cart_items.delete()

            return Response(
                OrderSerializer(order).data,
                status=status.HTTP_201_CREATED
            )
