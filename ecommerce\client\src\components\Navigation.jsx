import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { isAuthenticated, logout } from "../api/auth";

const Navigation = () => {
  const [authenticated, setAuthenticated] = useState(false);
  const [cartCount, setCartCount] = useState(0);
  const navigate = useNavigate();

  useEffect(() => {
    setAuthenticated(isAuthenticated());
    updateCartCount();
    
    // Listen for storage changes to update cart count
    const handleStorageChange = () => {
      updateCartCount();
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const updateCartCount = () => {
    const cart = JSON.parse(localStorage.getItem("cart")) || [];
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    setCartCount(totalItems);
  };

  const handleLogout = () => {
    logout();
    setAuthenticated(false);
    navigate("/");
  };

  return (
    <nav style={{
      backgroundColor: "#343a40",
      padding: "1rem 2rem",
      marginBottom: "20px"
    }}>
      <div style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        maxWidth: "1200px",
        margin: "0 auto"
      }}>
        <Link 
          to="/" 
          style={{
            color: "white",
            textDecoration: "none",
            fontSize: "1.5rem",
            fontWeight: "bold"
          }}
        >
          E-Commerce Store
        </Link>
        
        <div style={{ display: "flex", alignItems: "center", gap: "20px" }}>
          <Link 
            to="/" 
            style={{
              color: "white",
              textDecoration: "none",
              padding: "8px 16px",
              borderRadius: "4px",
              transition: "background-color 0.3s"
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = "#495057"}
            onMouseOut={(e) => e.target.style.backgroundColor = "transparent"}
          >
            Products
          </Link>
          
          <Link 
            to="/cart" 
            style={{
              color: "white",
              textDecoration: "none",
              padding: "8px 16px",
              borderRadius: "4px",
              position: "relative",
              transition: "background-color 0.3s"
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = "#495057"}
            onMouseOut={(e) => e.target.style.backgroundColor = "transparent"}
          >
            Cart
            {cartCount > 0 && (
              <span style={{
                position: "absolute",
                top: "-5px",
                right: "5px",
                backgroundColor: "#dc3545",
                color: "white",
                borderRadius: "50%",
                width: "20px",
                height: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "12px",
                fontWeight: "bold"
              }}>
                {cartCount}
              </span>
            )}
          </Link>
          
          {authenticated ? (
            <button
              onClick={handleLogout}
              style={{
                backgroundColor: "#dc3545",
                color: "white",
                border: "none",
                padding: "8px 16px",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "14px"
              }}
            >
              Logout
            </button>
          ) : (
            <div style={{ display: "flex", gap: "10px" }}>
              <Link 
                to="/login" 
                style={{
                  color: "white",
                  textDecoration: "none",
                  padding: "8px 16px",
                  border: "1px solid white",
                  borderRadius: "4px",
                  transition: "all 0.3s"
                }}
                onMouseOver={(e) => {
                  e.target.style.backgroundColor = "white";
                  e.target.style.color = "#343a40";
                }}
                onMouseOut={(e) => {
                  e.target.style.backgroundColor = "transparent";
                  e.target.style.color = "white";
                }}
              >
                Login
              </Link>
              
              <Link 
                to="/register" 
                style={{
                  backgroundColor: "#007bff",
                  color: "white",
                  textDecoration: "none",
                  padding: "8px 16px",
                  borderRadius: "4px",
                  transition: "background-color 0.3s"
                }}
                onMouseOver={(e) => e.target.style.backgroundColor = "#0056b3"}
                onMouseOut={(e) => e.target.style.backgroundColor = "#007bff"}
              >
                Register
              </Link>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
