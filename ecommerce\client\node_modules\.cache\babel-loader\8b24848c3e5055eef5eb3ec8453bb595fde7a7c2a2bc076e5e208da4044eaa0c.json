{"ast": null, "code": "var _jsxFileName = \"F:\\\\DJANGO\\\\ecommerce\\\\client\\\\src\\\\pages\\\\ProductsPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { fetchProducts } from \"../api/products\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductsPage = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  useEffect(() => {\n    loadProducts();\n  }, []);\n  const loadProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await fetchProducts();\n      setProducts(response.data);\n    } catch (error) {\n      console.error(\"Error fetching products:\", error);\n      setError(\"Failed to load products. Please try again later.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const addToCart = product => {\n    // Get existing cart from localStorage\n    const existingCart = JSON.parse(localStorage.getItem(\"cart\")) || [];\n\n    // Check if product already exists in cart\n    const existingItem = existingCart.find(item => item.id === product.id);\n    if (existingItem) {\n      // Increase quantity if product already in cart\n      existingItem.quantity += 1;\n    } else {\n      // Add new product to cart\n      existingCart.push({\n        ...product,\n        quantity: 1\n      });\n    }\n\n    // Save updated cart to localStorage\n    localStorage.setItem(\"cart\", JSON.stringify(existingCart));\n    alert(`${product.name} added to cart!`);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"center\",\n        padding: \"50px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Loading products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"center\",\n        padding: \"50px\",\n        color: \"red\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadProducts,\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: \"20px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Products\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), products.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"No products available.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"grid\",\n        gridTemplateColumns: \"repeat(auto-fill, minmax(300px, 1fr))\",\n        gap: \"20px\",\n        marginTop: \"20px\"\n      },\n      children: products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          border: \"1px solid #ddd\",\n          borderRadius: \"8px\",\n          padding: \"15px\",\n          backgroundColor: \"#f9f9f9\"\n        },\n        children: [product.image && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.image,\n          alt: product.name,\n          style: {\n            width: \"100%\",\n            height: \"200px\",\n            objectFit: \"cover\",\n            borderRadius: \"4px\",\n            marginBottom: \"10px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            margin: \"10px 0\"\n          },\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 15\n        }, this), product.description && /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: \"#666\",\n            fontSize: \"14px\",\n            marginBottom: \"10px\"\n          },\n          children: product.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            marginTop: \"15px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: \"18px\",\n              fontWeight: \"bold\",\n              color: \"#007bff\"\n            },\n            children: [\"$\", product.price]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => addToCart(product),\n            style: {\n              padding: \"8px 16px\",\n              backgroundColor: \"#28a745\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              cursor: \"pointer\",\n              fontSize: \"14px\"\n            },\n            children: \"Add to Cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 15\n        }, this), product.stock !== undefined && /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: \"12px\",\n            color: product.stock > 0 ? \"#28a745\" : \"#dc3545\",\n            marginTop: \"5px\"\n          },\n          children: product.stock > 0 ? `${product.stock} in stock` : \"Out of stock\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 17\n        }, this)]\n      }, product.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductsPage, \"kA5KSVzQR6dazF6X9o13RPFUYWw=\");\n_c = ProductsPage;\nexport default ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "fetchProducts", "jsxDEV", "_jsxDEV", "ProductsPage", "_s", "products", "setProducts", "loading", "setLoading", "error", "setError", "loadProducts", "response", "data", "console", "addToCart", "product", "existingCart", "JSON", "parse", "localStorage", "getItem", "existingItem", "find", "item", "id", "quantity", "push", "setItem", "stringify", "alert", "name", "style", "textAlign", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "onClick", "length", "display", "gridTemplateColumns", "gap", "marginTop", "map", "border", "borderRadius", "backgroundColor", "image", "src", "alt", "width", "height", "objectFit", "marginBottom", "margin", "description", "fontSize", "justifyContent", "alignItems", "fontWeight", "price", "cursor", "stock", "undefined", "_c", "$RefreshReg$"], "sources": ["F:/DJANGO/ecommerce/client/src/pages/ProductsPage.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { fetchProducts } from \"../api/products\";\n\nconst ProductsPage = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    loadProducts();\n  }, []);\n\n  const loadProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await fetchProducts();\n      setProducts(response.data);\n    } catch (error) {\n      console.error(\"Error fetching products:\", error);\n      setError(\"Failed to load products. Please try again later.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const addToCart = (product) => {\n    // Get existing cart from localStorage\n    const existingCart = JSON.parse(localStorage.getItem(\"cart\")) || [];\n    \n    // Check if product already exists in cart\n    const existingItem = existingCart.find(item => item.id === product.id);\n    \n    if (existingItem) {\n      // Increase quantity if product already in cart\n      existingItem.quantity += 1;\n    } else {\n      // Add new product to cart\n      existingCart.push({ ...product, quantity: 1 });\n    }\n    \n    // Save updated cart to localStorage\n    localStorage.setItem(\"cart\", JSON.stringify(existingCart));\n    \n    alert(`${product.name} added to cart!`);\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: \"center\", padding: \"50px\" }}>\n        <h2>Loading products...</h2>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{ textAlign: \"center\", padding: \"50px\", color: \"red\" }}>\n        <h2>Error</h2>\n        <p>{error}</p>\n        <button onClick={loadProducts}>Retry</button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: \"20px\" }}>\n      <h2>Products</h2>\n      \n      {products.length === 0 ? (\n        <p>No products available.</p>\n      ) : (\n        <div style={{ \n          display: \"grid\", \n          gridTemplateColumns: \"repeat(auto-fill, minmax(300px, 1fr))\", \n          gap: \"20px\",\n          marginTop: \"20px\"\n        }}>\n          {products.map(product => (\n            <div \n              key={product.id} \n              style={{\n                border: \"1px solid #ddd\",\n                borderRadius: \"8px\",\n                padding: \"15px\",\n                backgroundColor: \"#f9f9f9\"\n              }}\n            >\n              {product.image && (\n                <img \n                  src={product.image} \n                  alt={product.name}\n                  style={{\n                    width: \"100%\",\n                    height: \"200px\",\n                    objectFit: \"cover\",\n                    borderRadius: \"4px\",\n                    marginBottom: \"10px\"\n                  }}\n                />\n              )}\n              \n              <h4 style={{ margin: \"10px 0\" }}>{product.name}</h4>\n              \n              {product.description && (\n                <p style={{ \n                  color: \"#666\", \n                  fontSize: \"14px\",\n                  marginBottom: \"10px\"\n                }}>\n                  {product.description}\n                </p>\n              )}\n              \n              <div style={{ \n                display: \"flex\", \n                justifyContent: \"space-between\", \n                alignItems: \"center\",\n                marginTop: \"15px\"\n              }}>\n                <span style={{ \n                  fontSize: \"18px\", \n                  fontWeight: \"bold\", \n                  color: \"#007bff\" \n                }}>\n                  ${product.price}\n                </span>\n                \n                <button\n                  onClick={() => addToCart(product)}\n                  style={{\n                    padding: \"8px 16px\",\n                    backgroundColor: \"#28a745\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"4px\",\n                    cursor: \"pointer\",\n                    fontSize: \"14px\"\n                  }}\n                >\n                  Add to Cart\n                </button>\n              </div>\n              \n              {product.stock !== undefined && (\n                <p style={{ \n                  fontSize: \"12px\", \n                  color: product.stock > 0 ? \"#28a745\" : \"#dc3545\",\n                  marginTop: \"5px\"\n                }}>\n                  {product.stock > 0 ? `${product.stock} in stock` : \"Out of stock\"}\n                </p>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ProductsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACda,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMZ,aAAa,CAAC,CAAC;MACtCM,WAAW,CAACM,QAAQ,CAACC,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,kDAAkD,CAAC;IAC9D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMO,SAAS,GAAIC,OAAO,IAAK;IAC7B;IACA,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;;IAEnE;IACA,MAAMC,YAAY,GAAGL,YAAY,CAACM,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKT,OAAO,CAACS,EAAE,CAAC;IAEtE,IAAIH,YAAY,EAAE;MAChB;MACAA,YAAY,CAACI,QAAQ,IAAI,CAAC;IAC5B,CAAC,MAAM;MACL;MACAT,YAAY,CAACU,IAAI,CAAC;QAAE,GAAGX,OAAO;QAAEU,QAAQ,EAAE;MAAE,CAAC,CAAC;IAChD;;IAEA;IACAN,YAAY,CAACQ,OAAO,CAAC,MAAM,EAAEV,IAAI,CAACW,SAAS,CAACZ,YAAY,CAAC,CAAC;IAE1Da,KAAK,CAAC,GAAGd,OAAO,CAACe,IAAI,iBAAiB,CAAC;EACzC,CAAC;EAED,IAAIxB,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK8B,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,eACnDjC,OAAA;QAAAiC,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEV;EAEA,IAAI9B,KAAK,EAAE;IACT,oBACEP,OAAA;MAAK8B,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE,MAAM;QAAEM,KAAK,EAAE;MAAM,CAAE;MAAAL,QAAA,gBACjEjC,OAAA;QAAAiC,QAAA,EAAI;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdrC,OAAA;QAAAiC,QAAA,EAAI1B;MAAK;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdrC,OAAA;QAAQuC,OAAO,EAAE9B,YAAa;QAAAwB,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAEV;EAEA,oBACErC,OAAA;IAAK8B,KAAK,EAAE;MAAEE,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9BjC,OAAA;MAAAiC,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEhBlC,QAAQ,CAACqC,MAAM,KAAK,CAAC,gBACpBxC,OAAA;MAAAiC,QAAA,EAAG;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,gBAE7BrC,OAAA;MAAK8B,KAAK,EAAE;QACVW,OAAO,EAAE,MAAM;QACfC,mBAAmB,EAAE,uCAAuC;QAC5DC,GAAG,EAAE,MAAM;QACXC,SAAS,EAAE;MACb,CAAE;MAAAX,QAAA,EACC9B,QAAQ,CAAC0C,GAAG,CAAC/B,OAAO,iBACnBd,OAAA;QAEE8B,KAAK,EAAE;UACLgB,MAAM,EAAE,gBAAgB;UACxBC,YAAY,EAAE,KAAK;UACnBf,OAAO,EAAE,MAAM;UACfgB,eAAe,EAAE;QACnB,CAAE;QAAAf,QAAA,GAEDnB,OAAO,CAACmC,KAAK,iBACZjD,OAAA;UACEkD,GAAG,EAAEpC,OAAO,CAACmC,KAAM;UACnBE,GAAG,EAAErC,OAAO,CAACe,IAAK;UAClBC,KAAK,EAAE;YACLsB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,OAAO;YACfC,SAAS,EAAE,OAAO;YAClBP,YAAY,EAAE,KAAK;YACnBQ,YAAY,EAAE;UAChB;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,eAEDrC,OAAA;UAAI8B,KAAK,EAAE;YAAE0B,MAAM,EAAE;UAAS,CAAE;UAAAvB,QAAA,EAAEnB,OAAO,CAACe;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAEnDvB,OAAO,CAAC2C,WAAW,iBAClBzD,OAAA;UAAG8B,KAAK,EAAE;YACRQ,KAAK,EAAE,MAAM;YACboB,QAAQ,EAAE,MAAM;YAChBH,YAAY,EAAE;UAChB,CAAE;UAAAtB,QAAA,EACCnB,OAAO,CAAC2C;QAAW;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACJ,eAEDrC,OAAA;UAAK8B,KAAK,EAAE;YACVW,OAAO,EAAE,MAAM;YACfkB,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE,QAAQ;YACpBhB,SAAS,EAAE;UACb,CAAE;UAAAX,QAAA,gBACAjC,OAAA;YAAM8B,KAAK,EAAE;cACX4B,QAAQ,EAAE,MAAM;cAChBG,UAAU,EAAE,MAAM;cAClBvB,KAAK,EAAE;YACT,CAAE;YAAAL,QAAA,GAAC,GACA,EAACnB,OAAO,CAACgD,KAAK;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEPrC,OAAA;YACEuC,OAAO,EAAEA,CAAA,KAAM1B,SAAS,CAACC,OAAO,CAAE;YAClCgB,KAAK,EAAE;cACLE,OAAO,EAAE,UAAU;cACnBgB,eAAe,EAAE,SAAS;cAC1BV,KAAK,EAAE,OAAO;cACdQ,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBgB,MAAM,EAAE,SAAS;cACjBL,QAAQ,EAAE;YACZ,CAAE;YAAAzB,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELvB,OAAO,CAACkD,KAAK,KAAKC,SAAS,iBAC1BjE,OAAA;UAAG8B,KAAK,EAAE;YACR4B,QAAQ,EAAE,MAAM;YAChBpB,KAAK,EAAExB,OAAO,CAACkD,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YAChDpB,SAAS,EAAE;UACb,CAAE;UAAAX,QAAA,EACCnB,OAAO,CAACkD,KAAK,GAAG,CAAC,GAAG,GAAGlD,OAAO,CAACkD,KAAK,WAAW,GAAG;QAAc;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACJ;MAAA,GAxEIvB,OAAO,CAACS,EAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyEZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnC,EAAA,CA3JID,YAAY;AAAAiE,EAAA,GAAZjE,YAAY;AA6JlB,eAAeA,YAAY;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}