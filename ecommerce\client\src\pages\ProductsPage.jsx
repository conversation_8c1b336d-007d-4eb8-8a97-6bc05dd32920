import React, { useEffect, useState } from "react";
import { fetchProducts } from "../api/products";

const ProductsPage = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const response = await fetchProducts();
      setProducts(response.data);
    } catch (error) {
      console.error("Error fetching products:", error);
      setError("Failed to load products. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const addToCart = (product) => {
    // Get existing cart from localStorage
    const existingCart = JSON.parse(localStorage.getItem("cart")) || [];
    
    // Check if product already exists in cart
    const existingItem = existingCart.find(item => item.id === product.id);
    
    if (existingItem) {
      // Increase quantity if product already in cart
      existingItem.quantity += 1;
    } else {
      // Add new product to cart
      existingCart.push({ ...product, quantity: 1 });
    }
    
    // Save updated cart to localStorage
    localStorage.setItem("cart", JSON.stringify(existingCart));
    
    alert(`${product.name} added to cart!`);
  };

  if (loading) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <h2>Loading products...</h2>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ textAlign: "center", padding: "50px", color: "red" }}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={loadProducts}>Retry</button>
      </div>
    );
  }

  return (
    <div style={{ padding: "20px" }}>
      <h2>Products</h2>
      
      {products.length === 0 ? (
        <p>No products available.</p>
      ) : (
        <div style={{ 
          display: "grid", 
          gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))", 
          gap: "20px",
          marginTop: "20px"
        }}>
          {products.map(product => (
            <div 
              key={product.id} 
              style={{
                border: "1px solid #ddd",
                borderRadius: "8px",
                padding: "15px",
                backgroundColor: "#f9f9f9"
              }}
            >
              {product.image && (
                <img 
                  src={product.image} 
                  alt={product.name}
                  style={{
                    width: "100%",
                    height: "200px",
                    objectFit: "cover",
                    borderRadius: "4px",
                    marginBottom: "10px"
                  }}
                />
              )}
              
              <h4 style={{ margin: "10px 0" }}>{product.name}</h4>
              
              {product.description && (
                <p style={{ 
                  color: "#666", 
                  fontSize: "14px",
                  marginBottom: "10px"
                }}>
                  {product.description}
                </p>
              )}
              
              <div style={{ 
                display: "flex", 
                justifyContent: "space-between", 
                alignItems: "center",
                marginTop: "15px"
              }}>
                <span style={{ 
                  fontSize: "18px", 
                  fontWeight: "bold", 
                  color: "#007bff" 
                }}>
                  ${product.price}
                </span>
                
                <button
                  onClick={() => addToCart(product)}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: "#28a745",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    cursor: "pointer",
                    fontSize: "14px"
                  }}
                >
                  Add to Cart
                </button>
              </div>
              
              {product.stock !== undefined && (
                <p style={{ 
                  fontSize: "12px", 
                  color: product.stock > 0 ? "#28a745" : "#dc3545",
                  marginTop: "5px"
                }}>
                  {product.stock > 0 ? `${product.stock} in stock` : "Out of stock"}
                </p>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductsPage;
