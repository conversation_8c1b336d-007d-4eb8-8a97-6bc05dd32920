{"ast": null, "code": "import axios from \"axios\";\nconst BASE_URL = \"http://localhost:8000/api/\";\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Login function\nexport const login = async credentials => {\n  try {\n    const response = await api.post('auth/login/', credentials);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Register function\nexport const register = async userData => {\n  try {\n    const response = await api.post('auth/register/', userData);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Set authentication token in axios headers\nexport const setAuthToken = token => {\n  if (token) {\n    api.defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n    axios.defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n  } else {\n    delete api.defaults.headers.common[\"Authorization\"];\n    delete axios.defaults.headers.common[\"Authorization\"];\n  }\n};\n\n// Get token from localStorage\nexport const getToken = () => {\n  return localStorage.getItem(\"token\");\n};\n\n// Remove token from localStorage and axios headers\nexport const logout = () => {\n  localStorage.removeItem(\"token\");\n  setAuthToken(null);\n};\n\n// Check if user is authenticated\nexport const isAuthenticated = () => {\n  const token = getToken();\n  return !!token;\n};\n\n// Initialize auth token on app start\nexport const initializeAuth = () => {\n  const token = getToken();\n  if (token) {\n    setAuthToken(token);\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "BASE_URL", "api", "create", "baseURL", "headers", "login", "credentials", "response", "post", "error", "register", "userData", "setAuthToken", "token", "defaults", "common", "getToken", "localStorage", "getItem", "logout", "removeItem", "isAuthenticated", "initializeAuth"], "sources": ["F:/DJANGO/ecommerce/client/src/api/auth.js"], "sourcesContent": ["import axios from \"axios\";\n\nconst BASE_URL = \"http://localhost:8000/api/\";\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Login function\nexport const login = async (credentials) => {\n  try {\n    const response = await api.post('auth/login/', credentials);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Register function\nexport const register = async (userData) => {\n  try {\n    const response = await api.post('auth/register/', userData);\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Set authentication token in axios headers\nexport const setAuthToken = (token) => {\n  if (token) {\n    api.defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n    axios.defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n  } else {\n    delete api.defaults.headers.common[\"Authorization\"];\n    delete axios.defaults.headers.common[\"Authorization\"];\n  }\n};\n\n// Get token from localStorage\nexport const getToken = () => {\n  return localStorage.getItem(\"token\");\n};\n\n// Remove token from localStorage and axios headers\nexport const logout = () => {\n  localStorage.removeItem(\"token\");\n  setAuthToken(null);\n};\n\n// Check if user is authenticated\nexport const isAuthenticated = () => {\n  const token = getToken();\n  return !!token;\n};\n\n// Initialize auth token on app start\nexport const initializeAuth = () => {\n  const token = getToken();\n  if (token) {\n    setAuthToken(token);\n  }\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,QAAQ,GAAG,4BAA4B;;AAE7C;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,QAAQ;EACjBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,KAAK,GAAG,MAAOC,WAAW,IAAK;EAC1C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMN,GAAG,CAACO,IAAI,CAAC,aAAa,EAAEF,WAAW,CAAC;IAC3D,OAAOC,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,QAAQ,GAAG,MAAOC,QAAQ,IAAK;EAC1C,IAAI;IACF,MAAMJ,QAAQ,GAAG,MAAMN,GAAG,CAACO,IAAI,CAAC,gBAAgB,EAAEG,QAAQ,CAAC;IAC3D,OAAOJ,QAAQ;EACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,YAAY,GAAIC,KAAK,IAAK;EACrC,IAAIA,KAAK,EAAE;IACTZ,GAAG,CAACa,QAAQ,CAACV,OAAO,CAACW,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUF,KAAK,EAAE;IAChEd,KAAK,CAACe,QAAQ,CAACV,OAAO,CAACW,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUF,KAAK,EAAE;EACpE,CAAC,MAAM;IACL,OAAOZ,GAAG,CAACa,QAAQ,CAACV,OAAO,CAACW,MAAM,CAAC,eAAe,CAAC;IACnD,OAAOhB,KAAK,CAACe,QAAQ,CAACV,OAAO,CAACW,MAAM,CAAC,eAAe,CAAC;EACvD;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAC5B,OAAOC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;AACtC,CAAC;;AAED;AACA,OAAO,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAC1BF,YAAY,CAACG,UAAU,CAAC,OAAO,CAAC;EAChCR,YAAY,CAAC,IAAI,CAAC;AACpB,CAAC;;AAED;AACA,OAAO,MAAMS,eAAe,GAAGA,CAAA,KAAM;EACnC,MAAMR,KAAK,GAAGG,QAAQ,CAAC,CAAC;EACxB,OAAO,CAAC,CAACH,KAAK;AAChB,CAAC;;AAED;AACA,OAAO,MAAMS,cAAc,GAAGA,CAAA,KAAM;EAClC,MAAMT,KAAK,GAAGG,QAAQ,CAAC,CAAC;EACxB,IAAIH,KAAK,EAAE;IACTD,YAAY,CAACC,KAAK,CAAC;EACrB;AACF,CAAC;AAED,eAAeZ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}