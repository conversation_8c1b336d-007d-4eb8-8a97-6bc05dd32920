import axios from "axios";

const BASE_URL = "http://localhost:8000/api/";

// Create axios instance with base configuration
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Login function
export const login = async (credentials) => {
  try {
    const response = await api.post('auth/login/', credentials);
    return response;
  } catch (error) {
    throw error;
  }
};

// Register function
export const register = async (userData) => {
  try {
    const response = await api.post('auth/register/', userData);
    return response;
  } catch (error) {
    throw error;
  }
};

// Set authentication token in axios headers
export const setAuthToken = (token) => {
  if (token) {
    api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  } else {
    delete api.defaults.headers.common["Authorization"];
    delete axios.defaults.headers.common["Authorization"];
  }
};

// Get token from localStorage
export const getToken = () => {
  return localStorage.getItem("token");
};

// Remove token from localStorage and axios headers
export const logout = () => {
  localStorage.removeItem("token");
  setAuthToken(null);
};

// Check if user is authenticated
export const isAuthenticated = () => {
  const token = getToken();
  return !!token;
};

// Initialize auth token on app start
export const initializeAuth = () => {
  const token = getToken();
  if (token) {
    setAuthToken(token);
  }
};

export default api;
