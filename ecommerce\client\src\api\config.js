// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_URL || "http://localhost:8000/api/",
  TIMEOUT: 10000, // 10 seconds
};

// API Endpoints
export const ENDPOINTS = {
  AUTH: {
    LOGIN: "auth/login/",
    REGISTER: "auth/register/",
    REFRESH: "auth/refresh/",
  },
  PRODUCTS: {
    LIST: "products/",
    DETAIL: (id) => `products/${id}/`,
  },
  ORDERS: {
    LIST: "orders/",
    DETAIL: (id) => `orders/${id}/`,
    CREATE: "orders/",
  },
  CART: {
    LIST: "cart/",
    ADD: "cart/add/",
    UPDATE: (id) => `cart/${id}/`,
    REMOVE: (id) => `cart/${id}/`,
  },
};

export default API_CONFIG;
