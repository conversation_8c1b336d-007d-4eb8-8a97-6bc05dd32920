{"ast": null, "code": "var _jsxFileName = \"F:\\\\DJANGO\\\\ecommerce\\\\client\\\\src\\\\pages\\\\LoginPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { login, setAuthToken } from \"../api/auth\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  const [form, setForm] = useState({\n    username: \"\",\n    password: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n    try {\n      const response = await login(form);\n      const token = response.data.access;\n\n      // Store token in localStorage\n      localStorage.setItem(\"token\", token);\n\n      // Set token in axios headers\n      setAuthToken(token);\n\n      // Redirect to products page\n      navigate(\"/\");\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      console.error(\"Login error:\", error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Login failed. Please check your credentials.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: \"400px\",\n      margin: \"50px auto\",\n      padding: \"20px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: \"red\",\n        marginBottom: \"15px\",\n        padding: \"10px\",\n        border: \"1px solid red\",\n        borderRadius: \"4px\"\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"15px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"username\",\n          placeholder: \"Username\",\n          value: form.username,\n          onChange: handleChange,\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"10px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"15px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          name: \"password\",\n          placeholder: \"Password\",\n          value: form.password,\n          onChange: handleChange,\n          required: true,\n          style: {\n            width: \"100%\",\n            padding: \"10px\",\n            border: \"1px solid #ddd\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        style: {\n          width: \"100%\",\n          padding: \"12px\",\n          backgroundColor: loading ? \"#ccc\" : \"#007bff\",\n          color: \"white\",\n          border: \"none\",\n          borderRadius: \"4px\",\n          fontSize: \"16px\",\n          cursor: loading ? \"not-allowed\" : \"pointer\"\n        },\n        children: loading ? \"Logging in...\" : \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        textAlign: \"center\",\n        marginTop: \"20px\"\n      },\n      children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/register\",\n        children: \"Sign up\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 32\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"61Mz5iC+Q7uos2zXHx+TlF9KtPQ=\", false, function () {\n  return [useNavigate];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "login", "setAuthToken", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "form", "setForm", "username", "password", "loading", "setLoading", "error", "setError", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "response", "token", "data", "access", "localStorage", "setItem", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "console", "detail", "message", "style", "max<PERSON><PERSON><PERSON>", "margin", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "marginBottom", "border", "borderRadius", "onSubmit", "type", "placeholder", "onChange", "required", "width", "fontSize", "disabled", "backgroundColor", "cursor", "textAlign", "marginTop", "href", "_c", "$RefreshReg$"], "sources": ["F:/DJANGO/ecommerce/client/src/pages/LoginPage.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { login, setAuthToken } from \"../api/auth\";\n\nconst LoginPage = () => {\n  const [form, setForm] = useState({ username: \"\", password: \"\" });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setForm({\n      ...form,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    try {\n      const response = await login(form);\n      const token = response.data.access;\n      \n      // Store token in localStorage\n      localStorage.setItem(\"token\", token);\n      \n      // Set token in axios headers\n      setAuthToken(token);\n      \n      // Redirect to products page\n      navigate(\"/\");\n      \n    } catch (error) {\n      console.error(\"Login error:\", error);\n      setError(\n        error.response?.data?.detail || \n        error.response?.data?.message || \n        \"Login failed. Please check your credentials.\"\n      );\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div style={{ maxWidth: \"400px\", margin: \"50px auto\", padding: \"20px\" }}>\n      <h2>Login</h2>\n      \n      {error && (\n        <div style={{ \n          color: \"red\", \n          marginBottom: \"15px\", \n          padding: \"10px\", \n          border: \"1px solid red\", \n          borderRadius: \"4px\" \n        }}>\n          {error}\n        </div>\n      )}\n      \n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: \"15px\" }}>\n          <input\n            type=\"text\"\n            name=\"username\"\n            placeholder=\"Username\"\n            value={form.username}\n            onChange={handleChange}\n            required\n            style={{\n              width: \"100%\",\n              padding: \"10px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n        \n        <div style={{ marginBottom: \"15px\" }}>\n          <input\n            type=\"password\"\n            name=\"password\"\n            placeholder=\"Password\"\n            value={form.password}\n            onChange={handleChange}\n            required\n            style={{\n              width: \"100%\",\n              padding: \"10px\",\n              border: \"1px solid #ddd\",\n              borderRadius: \"4px\",\n              fontSize: \"16px\"\n            }}\n          />\n        </div>\n        \n        <button \n          type=\"submit\" \n          disabled={loading}\n          style={{\n            width: \"100%\",\n            padding: \"12px\",\n            backgroundColor: loading ? \"#ccc\" : \"#007bff\",\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"4px\",\n            fontSize: \"16px\",\n            cursor: loading ? \"not-allowed\" : \"pointer\"\n          }}\n        >\n          {loading ? \"Logging in...\" : \"Login\"}\n        </button>\n      </form>\n      \n      <p style={{ textAlign: \"center\", marginTop: \"20px\" }}>\n        Don't have an account? <a href=\"/register\">Sign up</a>\n      </p>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,KAAK,EAAEC,YAAY,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGT,QAAQ,CAAC;IAAEU,QAAQ,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EAChE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAMgB,YAAY,GAAIC,CAAC,IAAK;IAC1BT,OAAO,CAAC;MACN,GAAGD,IAAI;MACP,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBV,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMtB,KAAK,CAACM,IAAI,CAAC;MAClC,MAAMiB,KAAK,GAAGD,QAAQ,CAACE,IAAI,CAACC,MAAM;;MAElC;MACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,KAAK,CAAC;;MAEpC;MACAtB,YAAY,CAACsB,KAAK,CAAC;;MAEnB;MACAT,QAAQ,CAAC,GAAG,CAAC;IAEf,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAA,IAAAgB,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdC,OAAO,CAACpB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CACN,EAAAe,eAAA,GAAAhB,KAAK,CAACU,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBJ,IAAI,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBI,MAAM,OAAAH,gBAAA,GAC5BlB,KAAK,CAACU,QAAQ,cAAAQ,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBN,IAAI,cAAAO,qBAAA,uBAApBA,qBAAA,CAAsBG,OAAO,KAC7B,8CACF,CAAC;IACH,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACER,OAAA;IAAKgC,KAAK,EAAE;MAAEC,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACtEpC,OAAA;MAAAoC,QAAA,EAAI;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEb/B,KAAK,iBACJT,OAAA;MAAKgC,KAAK,EAAE;QACVS,KAAK,EAAE,KAAK;QACZC,YAAY,EAAE,MAAM;QACpBP,OAAO,EAAE,MAAM;QACfQ,MAAM,EAAE,eAAe;QACvBC,YAAY,EAAE;MAChB,CAAE;MAAAR,QAAA,EACC3B;IAAK;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDxC,OAAA;MAAM6C,QAAQ,EAAE5B,YAAa;MAAAmB,QAAA,gBAC3BpC,OAAA;QAAKgC,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAN,QAAA,eACnCpC,OAAA;UACE8C,IAAI,EAAC,MAAM;UACX/B,IAAI,EAAC,UAAU;UACfgC,WAAW,EAAC,UAAU;UACtB/B,KAAK,EAAEb,IAAI,CAACE,QAAS;UACrB2C,QAAQ,EAAEpC,YAAa;UACvBqC,QAAQ;UACRjB,KAAK,EAAE;YACLkB,KAAK,EAAE,MAAM;YACbf,OAAO,EAAE,MAAM;YACfQ,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE;UACZ;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENxC,OAAA;QAAKgC,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAN,QAAA,eACnCpC,OAAA;UACE8C,IAAI,EAAC,UAAU;UACf/B,IAAI,EAAC,UAAU;UACfgC,WAAW,EAAC,UAAU;UACtB/B,KAAK,EAAEb,IAAI,CAACG,QAAS;UACrB0C,QAAQ,EAAEpC,YAAa;UACvBqC,QAAQ;UACRjB,KAAK,EAAE;YACLkB,KAAK,EAAE,MAAM;YACbf,OAAO,EAAE,MAAM;YACfQ,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE;UACZ;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENxC,OAAA;QACE8C,IAAI,EAAC,QAAQ;QACbM,QAAQ,EAAE7C,OAAQ;QAClByB,KAAK,EAAE;UACLkB,KAAK,EAAE,MAAM;UACbf,OAAO,EAAE,MAAM;UACfkB,eAAe,EAAE9C,OAAO,GAAG,MAAM,GAAG,SAAS;UAC7CkC,KAAK,EAAE,OAAO;UACdE,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBO,QAAQ,EAAE,MAAM;UAChBG,MAAM,EAAE/C,OAAO,GAAG,aAAa,GAAG;QACpC,CAAE;QAAA6B,QAAA,EAED7B,OAAO,GAAG,eAAe,GAAG;MAAO;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEPxC,OAAA;MAAGgC,KAAK,EAAE;QAAEuB,SAAS,EAAE,QAAQ;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAApB,QAAA,GAAC,yBAC7B,eAAApC,OAAA;QAAGyD,IAAI,EAAC,WAAW;QAAArB,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACtC,EAAA,CAvHID,SAAS;EAAA,QAIIL,WAAW;AAAA;AAAA8D,EAAA,GAJxBzD,SAAS;AAyHf,eAAeA,SAAS;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}