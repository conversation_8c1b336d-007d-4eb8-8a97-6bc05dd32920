{"ast": null, "code": "/**\n * @param {{ protocol?: string, auth?: string, hostname?: string, port?: string, pathname?: string, search?: string, hash?: string, slashes?: boolean }} objURL\n * @returns {string}\n */\nfunction format(objURL) {\n  var protocol = objURL.protocol || \"\";\n  if (protocol && protocol.substr(-1) !== \":\") {\n    protocol += \":\";\n  }\n  var auth = objURL.auth || \"\";\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, \":\");\n    auth += \"@\";\n  }\n  var host = \"\";\n  if (objURL.hostname) {\n    host = auth + (objURL.hostname.indexOf(\":\") === -1 ? objURL.hostname : \"[\".concat(objURL.hostname, \"]\"));\n    if (objURL.port) {\n      host += \":\".concat(objURL.port);\n    }\n  }\n  var pathname = objURL.pathname || \"\";\n  if (objURL.slashes) {\n    host = \"//\".concat(host || \"\");\n    if (pathname && pathname.charAt(0) !== \"/\") {\n      pathname = \"/\".concat(pathname);\n    }\n  } else if (!host) {\n    host = \"\";\n  }\n  var search = objURL.search || \"\";\n  if (search && search.charAt(0) !== \"?\") {\n    search = \"?\".concat(search);\n  }\n  var hash = objURL.hash || \"\";\n  if (hash && hash.charAt(0) !== \"#\") {\n    hash = \"#\".concat(hash);\n  }\n  pathname = pathname.replace(/[?#]/g,\n  /**\n   * @param {string} match\n   * @returns {string}\n   */\n  function (match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace(\"#\", \"%23\");\n  return \"\".concat(protocol).concat(host).concat(pathname).concat(search).concat(hash);\n}\n\n/**\n * @param {URL & { fromCurrentScript?: boolean }} parsedURL\n * @returns {string}\n */\nfunction createSocketURL(parsedURL) {\n  var hostname = parsedURL.hostname;\n\n  // Node.js module parses it as `::`\n  // `new URL(urlString, [baseURLString])` parses it as '[::]'\n  var isInAddrAny = hostname === \"0.0.0.0\" || hostname === \"::\" || hostname === \"[::]\";\n\n  // why do we need this check?\n  // hostname n/a for file protocol (example, when using electron, ionic)\n  // see: https://github.com/webpack/webpack-dev-server/pull/384\n  if (isInAddrAny && self.location.hostname && self.location.protocol.indexOf(\"http\") === 0) {\n    hostname = self.location.hostname;\n  }\n  var socketURLProtocol = parsedURL.protocol || self.location.protocol;\n\n  // When https is used in the app, secure web sockets are always necessary because the browser doesn't accept non-secure web sockets.\n  if (socketURLProtocol === \"auto:\" || hostname && isInAddrAny && self.location.protocol === \"https:\") {\n    socketURLProtocol = self.location.protocol;\n  }\n  socketURLProtocol = socketURLProtocol.replace(/^(?:http|.+-extension|file)/i, \"ws\");\n  var socketURLAuth = \"\";\n\n  // `new URL(urlString, [baseURLstring])` doesn't have `auth` property\n  // Parse authentication credentials in case we need them\n  if (parsedURL.username) {\n    socketURLAuth = parsedURL.username;\n\n    // Since HTTP basic authentication does not allow empty username,\n    // we only include password if the username is not empty.\n    if (parsedURL.password) {\n      // Result: <username>:<password>\n      socketURLAuth = socketURLAuth.concat(\":\", parsedURL.password);\n    }\n  }\n\n  // In case the host is a raw IPv6 address, it can be enclosed in\n  // the brackets as the brackets are needed in the final URL string.\n  // Need to remove those as url.format blindly adds its own set of brackets\n  // if the host string contains colons. That would lead to non-working\n  // double brackets (e.g. [[::]]) host\n  //\n  // All of these web socket url params are optionally passed in through resourceQuery,\n  // so we need to fall back to the default if they are not provided\n  var socketURLHostname = (hostname || self.location.hostname || \"localhost\").replace(/^\\[(.*)\\]$/, \"$1\");\n  var socketURLPort = parsedURL.port;\n  if (!socketURLPort || socketURLPort === \"0\") {\n    socketURLPort = self.location.port;\n  }\n\n  // If path is provided it'll be passed in via the resourceQuery as a\n  // query param so it has to be parsed out of the querystring in order for the\n  // client to open the socket to the correct location.\n  var socketURLPathname = \"/ws\";\n  if (parsedURL.pathname && !parsedURL.fromCurrentScript) {\n    socketURLPathname = parsedURL.pathname;\n  }\n  return format({\n    protocol: socketURLProtocol,\n    auth: socketURLAuth,\n    hostname: socketURLHostname,\n    port: socketURLPort,\n    pathname: socketURLPathname,\n    slashes: true\n  });\n}\nexport default createSocketURL;", "map": {"version": 3, "names": ["format", "objURL", "protocol", "substr", "auth", "encodeURIComponent", "replace", "host", "hostname", "indexOf", "concat", "port", "pathname", "slashes", "char<PERSON>t", "search", "hash", "match", "createSocketURL", "parsedURL", "isInAddrAny", "self", "location", "socketURLProtocol", "socketURLAuth", "username", "password", "socketURLHostname", "socketURLPort", "socketURLPathname", "fromCurrentScript"], "sources": ["F:/DJANGO/ecommerce/client/node_modules/webpack-dev-server/client/utils/createSocketURL.js"], "sourcesContent": ["/**\n * @param {{ protocol?: string, auth?: string, hostname?: string, port?: string, pathname?: string, search?: string, hash?: string, slashes?: boolean }} objURL\n * @returns {string}\n */\nfunction format(objURL) {\n  var protocol = objURL.protocol || \"\";\n  if (protocol && protocol.substr(-1) !== \":\") {\n    protocol += \":\";\n  }\n  var auth = objURL.auth || \"\";\n  if (auth) {\n    auth = encodeURIComponent(auth);\n    auth = auth.replace(/%3A/i, \":\");\n    auth += \"@\";\n  }\n  var host = \"\";\n  if (objURL.hostname) {\n    host = auth + (objURL.hostname.indexOf(\":\") === -1 ? objURL.hostname : \"[\".concat(objURL.hostname, \"]\"));\n    if (objURL.port) {\n      host += \":\".concat(objURL.port);\n    }\n  }\n  var pathname = objURL.pathname || \"\";\n  if (objURL.slashes) {\n    host = \"//\".concat(host || \"\");\n    if (pathname && pathname.charAt(0) !== \"/\") {\n      pathname = \"/\".concat(pathname);\n    }\n  } else if (!host) {\n    host = \"\";\n  }\n  var search = objURL.search || \"\";\n  if (search && search.charAt(0) !== \"?\") {\n    search = \"?\".concat(search);\n  }\n  var hash = objURL.hash || \"\";\n  if (hash && hash.charAt(0) !== \"#\") {\n    hash = \"#\".concat(hash);\n  }\n  pathname = pathname.replace(/[?#]/g,\n  /**\n   * @param {string} match\n   * @returns {string}\n   */\n  function (match) {\n    return encodeURIComponent(match);\n  });\n  search = search.replace(\"#\", \"%23\");\n  return \"\".concat(protocol).concat(host).concat(pathname).concat(search).concat(hash);\n}\n\n/**\n * @param {URL & { fromCurrentScript?: boolean }} parsedURL\n * @returns {string}\n */\nfunction createSocketURL(parsedURL) {\n  var hostname = parsedURL.hostname;\n\n  // Node.js module parses it as `::`\n  // `new URL(urlString, [baseURLString])` parses it as '[::]'\n  var isInAddrAny = hostname === \"0.0.0.0\" || hostname === \"::\" || hostname === \"[::]\";\n\n  // why do we need this check?\n  // hostname n/a for file protocol (example, when using electron, ionic)\n  // see: https://github.com/webpack/webpack-dev-server/pull/384\n  if (isInAddrAny && self.location.hostname && self.location.protocol.indexOf(\"http\") === 0) {\n    hostname = self.location.hostname;\n  }\n  var socketURLProtocol = parsedURL.protocol || self.location.protocol;\n\n  // When https is used in the app, secure web sockets are always necessary because the browser doesn't accept non-secure web sockets.\n  if (socketURLProtocol === \"auto:\" || hostname && isInAddrAny && self.location.protocol === \"https:\") {\n    socketURLProtocol = self.location.protocol;\n  }\n  socketURLProtocol = socketURLProtocol.replace(/^(?:http|.+-extension|file)/i, \"ws\");\n  var socketURLAuth = \"\";\n\n  // `new URL(urlString, [baseURLstring])` doesn't have `auth` property\n  // Parse authentication credentials in case we need them\n  if (parsedURL.username) {\n    socketURLAuth = parsedURL.username;\n\n    // Since HTTP basic authentication does not allow empty username,\n    // we only include password if the username is not empty.\n    if (parsedURL.password) {\n      // Result: <username>:<password>\n      socketURLAuth = socketURLAuth.concat(\":\", parsedURL.password);\n    }\n  }\n\n  // In case the host is a raw IPv6 address, it can be enclosed in\n  // the brackets as the brackets are needed in the final URL string.\n  // Need to remove those as url.format blindly adds its own set of brackets\n  // if the host string contains colons. That would lead to non-working\n  // double brackets (e.g. [[::]]) host\n  //\n  // All of these web socket url params are optionally passed in through resourceQuery,\n  // so we need to fall back to the default if they are not provided\n  var socketURLHostname = (hostname || self.location.hostname || \"localhost\").replace(/^\\[(.*)\\]$/, \"$1\");\n  var socketURLPort = parsedURL.port;\n  if (!socketURLPort || socketURLPort === \"0\") {\n    socketURLPort = self.location.port;\n  }\n\n  // If path is provided it'll be passed in via the resourceQuery as a\n  // query param so it has to be parsed out of the querystring in order for the\n  // client to open the socket to the correct location.\n  var socketURLPathname = \"/ws\";\n  if (parsedURL.pathname && !parsedURL.fromCurrentScript) {\n    socketURLPathname = parsedURL.pathname;\n  }\n  return format({\n    protocol: socketURLProtocol,\n    auth: socketURLAuth,\n    hostname: socketURLHostname,\n    port: socketURLPort,\n    pathname: socketURLPathname,\n    slashes: true\n  });\n}\nexport default createSocketURL;"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,MAAM,EAAE;EACtB,IAAIC,QAAQ,GAAGD,MAAM,CAACC,QAAQ,IAAI,EAAE;EACpC,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC3CD,QAAQ,IAAI,GAAG;EACjB;EACA,IAAIE,IAAI,GAAGH,MAAM,CAACG,IAAI,IAAI,EAAE;EAC5B,IAAIA,IAAI,EAAE;IACRA,IAAI,GAAGC,kBAAkB,CAACD,IAAI,CAAC;IAC/BA,IAAI,GAAGA,IAAI,CAACE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IAChCF,IAAI,IAAI,GAAG;EACb;EACA,IAAIG,IAAI,GAAG,EAAE;EACb,IAAIN,MAAM,CAACO,QAAQ,EAAE;IACnBD,IAAI,GAAGH,IAAI,IAAIH,MAAM,CAACO,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAGR,MAAM,CAACO,QAAQ,GAAG,GAAG,CAACE,MAAM,CAACT,MAAM,CAACO,QAAQ,EAAE,GAAG,CAAC,CAAC;IACxG,IAAIP,MAAM,CAACU,IAAI,EAAE;MACfJ,IAAI,IAAI,GAAG,CAACG,MAAM,CAACT,MAAM,CAACU,IAAI,CAAC;IACjC;EACF;EACA,IAAIC,QAAQ,GAAGX,MAAM,CAACW,QAAQ,IAAI,EAAE;EACpC,IAAIX,MAAM,CAACY,OAAO,EAAE;IAClBN,IAAI,GAAG,IAAI,CAACG,MAAM,CAACH,IAAI,IAAI,EAAE,CAAC;IAC9B,IAAIK,QAAQ,IAAIA,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC1CF,QAAQ,GAAG,GAAG,CAACF,MAAM,CAACE,QAAQ,CAAC;IACjC;EACF,CAAC,MAAM,IAAI,CAACL,IAAI,EAAE;IAChBA,IAAI,GAAG,EAAE;EACX;EACA,IAAIQ,MAAM,GAAGd,MAAM,CAACc,MAAM,IAAI,EAAE;EAChC,IAAIA,MAAM,IAAIA,MAAM,CAACD,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACtCC,MAAM,GAAG,GAAG,CAACL,MAAM,CAACK,MAAM,CAAC;EAC7B;EACA,IAAIC,IAAI,GAAGf,MAAM,CAACe,IAAI,IAAI,EAAE;EAC5B,IAAIA,IAAI,IAAIA,IAAI,CAACF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClCE,IAAI,GAAG,GAAG,CAACN,MAAM,CAACM,IAAI,CAAC;EACzB;EACAJ,QAAQ,GAAGA,QAAQ,CAACN,OAAO,CAAC,OAAO;EACnC;AACF;AACA;AACA;EACE,UAAUW,KAAK,EAAE;IACf,OAAOZ,kBAAkB,CAACY,KAAK,CAAC;EAClC,CAAC,CAAC;EACFF,MAAM,GAAGA,MAAM,CAACT,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;EACnC,OAAO,EAAE,CAACI,MAAM,CAACR,QAAQ,CAAC,CAACQ,MAAM,CAACH,IAAI,CAAC,CAACG,MAAM,CAACE,QAAQ,CAAC,CAACF,MAAM,CAACK,MAAM,CAAC,CAACL,MAAM,CAACM,IAAI,CAAC;AACtF;;AAEA;AACA;AACA;AACA;AACA,SAASE,eAAeA,CAACC,SAAS,EAAE;EAClC,IAAIX,QAAQ,GAAGW,SAAS,CAACX,QAAQ;;EAEjC;EACA;EACA,IAAIY,WAAW,GAAGZ,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,MAAM;;EAEpF;EACA;EACA;EACA,IAAIY,WAAW,IAAIC,IAAI,CAACC,QAAQ,CAACd,QAAQ,IAAIa,IAAI,CAACC,QAAQ,CAACpB,QAAQ,CAACO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IACzFD,QAAQ,GAAGa,IAAI,CAACC,QAAQ,CAACd,QAAQ;EACnC;EACA,IAAIe,iBAAiB,GAAGJ,SAAS,CAACjB,QAAQ,IAAImB,IAAI,CAACC,QAAQ,CAACpB,QAAQ;;EAEpE;EACA,IAAIqB,iBAAiB,KAAK,OAAO,IAAIf,QAAQ,IAAIY,WAAW,IAAIC,IAAI,CAACC,QAAQ,CAACpB,QAAQ,KAAK,QAAQ,EAAE;IACnGqB,iBAAiB,GAAGF,IAAI,CAACC,QAAQ,CAACpB,QAAQ;EAC5C;EACAqB,iBAAiB,GAAGA,iBAAiB,CAACjB,OAAO,CAAC,8BAA8B,EAAE,IAAI,CAAC;EACnF,IAAIkB,aAAa,GAAG,EAAE;;EAEtB;EACA;EACA,IAAIL,SAAS,CAACM,QAAQ,EAAE;IACtBD,aAAa,GAAGL,SAAS,CAACM,QAAQ;;IAElC;IACA;IACA,IAAIN,SAAS,CAACO,QAAQ,EAAE;MACtB;MACAF,aAAa,GAAGA,aAAa,CAACd,MAAM,CAAC,GAAG,EAAES,SAAS,CAACO,QAAQ,CAAC;IAC/D;EACF;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,iBAAiB,GAAG,CAACnB,QAAQ,IAAIa,IAAI,CAACC,QAAQ,CAACd,QAAQ,IAAI,WAAW,EAAEF,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC;EACvG,IAAIsB,aAAa,GAAGT,SAAS,CAACR,IAAI;EAClC,IAAI,CAACiB,aAAa,IAAIA,aAAa,KAAK,GAAG,EAAE;IAC3CA,aAAa,GAAGP,IAAI,CAACC,QAAQ,CAACX,IAAI;EACpC;;EAEA;EACA;EACA;EACA,IAAIkB,iBAAiB,GAAG,KAAK;EAC7B,IAAIV,SAAS,CAACP,QAAQ,IAAI,CAACO,SAAS,CAACW,iBAAiB,EAAE;IACtDD,iBAAiB,GAAGV,SAAS,CAACP,QAAQ;EACxC;EACA,OAAOZ,MAAM,CAAC;IACZE,QAAQ,EAAEqB,iBAAiB;IAC3BnB,IAAI,EAAEoB,aAAa;IACnBhB,QAAQ,EAAEmB,iBAAiB;IAC3BhB,IAAI,EAAEiB,aAAa;IACnBhB,QAAQ,EAAEiB,iBAAiB;IAC3BhB,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AACA,eAAeK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}