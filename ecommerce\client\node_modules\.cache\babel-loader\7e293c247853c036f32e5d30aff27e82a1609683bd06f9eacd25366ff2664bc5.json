{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.1.toString);\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "id", "postfix", "Math", "random", "toString", "module", "exports", "key", "undefined"], "sources": ["F:/DJANGO/ecommerce/client/node_modules/core-js-pure/internals/uid.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.1.toString);\n\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAE/D,IAAIC,EAAE,GAAG,CAAC;AACV,IAAIC,OAAO,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAGN,WAAW,CAAC,GAAG,CAACM,QAAQ,CAAC;AAExCC,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAE;EAC9B,OAAO,SAAS,IAAIA,GAAG,KAAKC,SAAS,GAAG,EAAE,GAAGD,GAAG,CAAC,GAAG,IAAI,GAAGH,QAAQ,CAAC,EAAEJ,EAAE,GAAGC,OAAO,EAAE,EAAE,CAAC;AACzF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}