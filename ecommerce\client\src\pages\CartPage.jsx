import React, { useState, useEffect } from "react";

const CartPage = () => {
  const [cart, setCart] = useState([]);

  useEffect(() => {
    loadCart();
  }, []);

  const loadCart = () => {
    const savedCart = JSON.parse(localStorage.getItem("cart")) || [];
    setCart(savedCart);
  };

  const updateCart = (updatedCart) => {
    localStorage.setItem("cart", JSON.stringify(updatedCart));
    setCart(updatedCart);
  };

  const removeItem = (id) => {
    const updatedCart = cart.filter(item => item.id !== id);
    updateCart(updatedCart);
  };

  const updateQuantity = (id, newQuantity) => {
    if (newQuantity <= 0) {
      removeItem(id);
      return;
    }

    const updatedCart = cart.map(item =>
      item.id === id ? { ...item, quantity: newQuantity } : item
    );
    updateCart(updatedCart);
  };

  const clearCart = () => {
    updateCart([]);
  };

  const calculateTotal = () => {
    return cart.reduce((total, item) => {
      return total + (parseFloat(item.price) * item.quantity);
    }, 0).toFixed(2);
  };

  const calculateItemTotal = (item) => {
    return (parseFloat(item.price) * item.quantity).toFixed(2);
  };

  if (cart.length === 0) {
    return (
      <div style={{ padding: "20px", textAlign: "center" }}>
        <h2>Your Cart</h2>
        <p>Your cart is empty.</p>
        <a 
          href="/" 
          style={{
            display: "inline-block",
            marginTop: "20px",
            padding: "10px 20px",
            backgroundColor: "#007bff",
            color: "white",
            textDecoration: "none",
            borderRadius: "4px"
          }}
        >
          Continue Shopping
        </a>
      </div>
    );
  }

  return (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <div style={{ 
        display: "flex", 
        justifyContent: "space-between", 
        alignItems: "center",
        marginBottom: "20px"
      }}>
        <h2>Your Cart ({cart.length} items)</h2>
        <button
          onClick={clearCart}
          style={{
            padding: "8px 16px",
            backgroundColor: "#dc3545",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer"
          }}
        >
          Clear Cart
        </button>
      </div>

      <div style={{ marginBottom: "30px" }}>
        {cart.map(item => (
          <div 
            key={item.id}
            style={{
              display: "flex",
              alignItems: "center",
              padding: "15px",
              border: "1px solid #ddd",
              borderRadius: "8px",
              marginBottom: "15px",
              backgroundColor: "#f9f9f9"
            }}
          >
            {item.image && (
              <img 
                src={item.image} 
                alt={item.name}
                style={{
                  width: "80px",
                  height: "80px",
                  objectFit: "cover",
                  borderRadius: "4px",
                  marginRight: "15px"
                }}
              />
            )}
            
            <div style={{ flex: 1 }}>
              <h4 style={{ margin: "0 0 5px 0" }}>{item.name}</h4>
              <p style={{ margin: "0", color: "#666", fontSize: "14px" }}>
                ${item.price} each
              </p>
            </div>
            
            <div style={{ 
              display: "flex", 
              alignItems: "center", 
              gap: "10px",
              marginRight: "20px"
            }}>
              <button
                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                style={{
                  width: "30px",
                  height: "30px",
                  border: "1px solid #ddd",
                  backgroundColor: "white",
                  cursor: "pointer",
                  borderRadius: "4px"
                }}
              >
                -
              </button>
              
              <span style={{ 
                minWidth: "30px", 
                textAlign: "center",
                fontSize: "16px",
                fontWeight: "bold"
              }}>
                {item.quantity}
              </span>
              
              <button
                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                style={{
                  width: "30px",
                  height: "30px",
                  border: "1px solid #ddd",
                  backgroundColor: "white",
                  cursor: "pointer",
                  borderRadius: "4px"
                }}
              >
                +
              </button>
            </div>
            
            <div style={{ 
              minWidth: "80px", 
              textAlign: "right",
              marginRight: "15px"
            }}>
              <strong>${calculateItemTotal(item)}</strong>
            </div>
            
            <button
              onClick={() => removeItem(item.id)}
              style={{
                padding: "5px 10px",
                backgroundColor: "#dc3545",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "12px"
              }}
            >
              Remove
            </button>
          </div>
        ))}
      </div>

      <div style={{
        borderTop: "2px solid #ddd",
        paddingTop: "20px",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center"
      }}>
        <div>
          <h3 style={{ margin: "0" }}>
            Total: ${calculateTotal()}
          </h3>
        </div>
        
        <div style={{ display: "flex", gap: "10px" }}>
          <a 
            href="/"
            style={{
              padding: "12px 24px",
              backgroundColor: "#6c757d",
              color: "white",
              textDecoration: "none",
              borderRadius: "4px",
              border: "none",
              cursor: "pointer"
            }}
          >
            Continue Shopping
          </a>
          
          <button
            style={{
              padding: "12px 24px",
              backgroundColor: "#28a745",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "16px"
            }}
            onClick={() => alert("Checkout functionality coming soon!")}
          >
            Proceed to Checkout
          </button>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
