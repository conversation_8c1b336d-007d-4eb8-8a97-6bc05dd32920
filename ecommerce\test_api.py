#!/usr/bin/env python
"""
Test script to verify all API endpoints are working correctly.
Run this after starting the Django server with: python manage.py runserver
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"
AUTH_URL = f"{BASE_URL}/auth"

def test_api_endpoints():
    print("🧪 Testing Django E-commerce API Endpoints")
    print("=" * 50)
    
    # Test 1: Check if API root is accessible
    print("\n1. Testing API Root...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"   ✅ API Root: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Available endpoints: {list(data.keys())}")
    except Exception as e:
        print(f"   ❌ API Root failed: {e}")
    
    # Test 2: Check products endpoint
    print("\n2. Testing Products Endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/products/")
        print(f"   ✅ Products GET: {response.status_code}")
        if response.status_code == 200:
            products = response.json()
            print(f"   Found {len(products)} products")
    except Exception as e:
        print(f"   ❌ Products GET failed: {e}")
    
    # Test 3: Check categories endpoint
    print("\n3. Testing Categories Endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/categories/")
        print(f"   ✅ Categories GET: {response.status_code}")
        if response.status_code == 200:
            categories = response.json()
            print(f"   Found {len(categories)} categories")
    except Exception as e:
        print(f"   ❌ Categories GET failed: {e}")
    
    # Test 4: Check auth endpoints
    print("\n4. Testing Auth Endpoints...")
    try:
        # Test register endpoint
        response = requests.get(f"{AUTH_URL}/register/")
        print(f"   ✅ Register endpoint: {response.status_code}")
        
        # Test login endpoint
        response = requests.get(f"{AUTH_URL}/login/")
        print(f"   ✅ Login endpoint: {response.status_code}")
        
        # Test token refresh endpoint
        response = requests.get(f"{AUTH_URL}/token/refresh/")
        print(f"   ✅ Token refresh endpoint: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Auth endpoints failed: {e}")
    
    # Test 5: Check cart endpoint (requires auth)
    print("\n5. Testing Cart Endpoint (without auth)...")
    try:
        response = requests.get(f"{BASE_URL}/cart/")
        print(f"   ✅ Cart GET (no auth): {response.status_code}")
        if response.status_code == 401:
            print("   ✅ Correctly requires authentication")
    except Exception as e:
        print(f"   ❌ Cart GET failed: {e}")
    
    # Test 6: Check orders endpoint (requires auth)
    print("\n6. Testing Orders Endpoint (without auth)...")
    try:
        response = requests.get(f"{BASE_URL}/orders/")
        print(f"   ✅ Orders GET (no auth): {response.status_code}")
        if response.status_code == 401:
            print("   ✅ Correctly requires authentication")
    except Exception as e:
        print(f"   ❌ Orders GET failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API Endpoint Testing Complete!")
    print("\n📋 Summary of your API endpoints:")
    print("   • GET  /api/products/          - List all products")
    print("   • POST /api/products/          - Create product (auth required)")
    print("   • GET  /api/products/{id}/     - Get specific product")
    print("   • PUT  /api/products/{id}/     - Update product (owner/admin only)")
    print("   • DELETE /api/products/{id}/   - Delete product (owner/admin only)")
    print("   • GET  /api/categories/        - List categories (admin only)")
    print("   • POST /api/categories/        - Create category (admin only)")
    print("   • GET  /api/cart/              - View cart items (auth required)")
    print("   • POST /api/cart/              - Add to cart (auth required)")
    print("   • GET  /api/orders/            - View order history (auth required)")
    print("   • POST /api/orders/            - Place order (auth required)")
    print("   • POST /api/auth/register/     - Register new user")
    print("   • POST /api/auth/login/        - Login (get JWT token)")
    print("   • POST /api/auth/token/refresh/ - Refresh JWT token")

if __name__ == "__main__":
    test_api_endpoints()
