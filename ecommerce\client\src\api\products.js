import axios from "axios";

const BASE_URL = "http://localhost:8000/api/";

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Fetch all products
export const fetchProducts = async () => {
  try {
    const response = await api.get('products/');
    return response;
  } catch (error) {
    throw error;
  }
};

// Fetch single product by ID
export const fetchProduct = async (id) => {
  try {
    const response = await api.get(`products/${id}/`);
    return response;
  } catch (error) {
    throw error;
  }
};

// Create new product (admin only)
export const createProduct = async (productData) => {
  try {
    const response = await api.post('products/', productData);
    return response;
  } catch (error) {
    throw error;
  }
};

// Update product (admin only)
export const updateProduct = async (id, productData) => {
  try {
    const response = await api.put(`products/${id}/`, productData);
    return response;
  } catch (error) {
    throw error;
  }
};

// Delete product (admin only)
export const deleteProduct = async (id) => {
  try {
    const response = await api.delete(`products/${id}/`);
    return response;
  } catch (error) {
    throw error;
  }
};

export default api;
