[{"F:\\DJANGO\\ecommerce\\client\\src\\index.js": "1", "F:\\DJANGO\\ecommerce\\client\\src\\reportWebVitals.js": "2", "F:\\DJANGO\\ecommerce\\client\\src\\App.js": "3", "F:\\DJANGO\\ecommerce\\client\\src\\api\\auth.js": "4", "F:\\DJANGO\\ecommerce\\client\\src\\pages\\ProductsPage.jsx": "5", "F:\\DJANGO\\ecommerce\\client\\src\\pages\\LoginPage.jsx": "6", "F:\\DJANGO\\ecommerce\\client\\src\\pages\\RegisterPage.jsx": "7", "F:\\DJANGO\\ecommerce\\client\\src\\components\\Navigation.jsx": "8", "F:\\DJANGO\\ecommerce\\client\\src\\pages\\CartPage.jsx": "9", "F:\\DJANGO\\ecommerce\\client\\src\\api\\products.js": "10"}, {"size": 535, "mtime": 1750344098534, "results": "11", "hashOfConfig": "12"}, {"size": 362, "mtime": 1750344098758, "results": "13", "hashOfConfig": "12"}, {"size": 966, "mtime": 1750346623468, "results": "14", "hashOfConfig": "12"}, {"size": 1569, "mtime": 1750346465376, "results": "15", "hashOfConfig": "12"}, {"size": 4586, "mtime": 1750346524696, "results": "16", "hashOfConfig": "12"}, {"size": 3227, "mtime": 1750346498952, "results": "17", "hashOfConfig": "12"}, {"size": 4303, "mtime": 1750346573865, "results": "18", "hashOfConfig": "12"}, {"size": 5198, "mtime": 1750346594042, "results": "19", "hashOfConfig": "12"}, {"size": 6676, "mtime": 1750346555873, "results": "20", "hashOfConfig": "12"}, {"size": 1288, "mtime": 1750346479475, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pxqv2p", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\DJANGO\\ecommerce\\client\\src\\index.js", [], [], "F:\\DJANGO\\ecommerce\\client\\src\\reportWebVitals.js", [], [], "F:\\DJANGO\\ecommerce\\client\\src\\App.js", [], [], "F:\\DJANGO\\ecommerce\\client\\src\\api\\auth.js", [], [], "F:\\DJANGO\\ecommerce\\client\\src\\pages\\ProductsPage.jsx", [], [], "F:\\DJANGO\\ecommerce\\client\\src\\pages\\LoginPage.jsx", [], [], "F:\\DJANGO\\ecommerce\\client\\src\\pages\\RegisterPage.jsx", [], [], "F:\\DJANGO\\ecommerce\\client\\src\\components\\Navigation.jsx", [], [], "F:\\DJANGO\\ecommerce\\client\\src\\pages\\CartPage.jsx", [], [], "F:\\DJANGO\\ecommerce\\client\\src\\api\\products.js", [], []]